import numpy as np
import open3d as o3d
from camera import Helios2Camera

if __name__ == "__main__":
    camera = Helios2Camera()
    
    pcd = camera.get_pointcloud()
    assert pcd is not None
    
    # # ROI box (including conveyor)
    # pcd = pcd.crop(o3d.geometry.AxisAlignedBoundingBox(min_bound=(-330, -680, 400), max_bound=(250, 500, 2000)))
    # ROI box (without conveyor)
    pcd = pcd.crop(o3d.geometry.AxisAlignedBoundingBox(min_bound=(-330, -680, 400), max_bound=(250, 500, 1350)))
    
    
    points = np.asarray(pcd.points)
    print(f"Min and max X: {points[:, 0].min():.3f} to {points[:, 0].max():.3f} mm")
    print(f"Min and max Y: {points[:, 1].min():.3f} to {points[:, 1].max():.3f} mm")
    print(f"Min and max Z: {points[:, 2].min():.3f} to {points[:, 2].max():.3f} mm")
    
    
    # Get top (low Z value) of box by taking 10th percentile of Z values
    z_values = points[:, 2]
    top_box_z = np.percentile(z_values, 10)
    print(f"Top of box (10th percentile Z): {top_box_z:.3f} mm")
    
    # Isolate top of box (50 mm margin around top_box_z)
    pcd = pcd.crop(o3d.geometry.AxisAlignedBoundingBox(min_bound=(-np.inf, -np.inf, top_box_z - 50), max_bound=(np.inf, np.inf, top_box_z + 50)))
    

    # Set up visualization
    vis = o3d.visualization.Visualizer()  # type: ignore
    vis.create_window(window_name=f"PLY Viewer")
    vis.add_geometry(pcd)

    # Set rendering options for better visualization
    render_option = vis.get_render_option()
    render_option.point_size = 2.0  # Make points more visible
    render_option.background_color = np.array([0.1, 0.1, 0.1])  # Dark background

    # Run the visualizer
    vis.run()
    vis.destroy_window()
