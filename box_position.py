import numpy as np
import open3d as o3d
from camera import Helios2Camera

if __name__ == "__main__":
    camera = Helios2Camera()
    
    pcd = camera.get_pointcloud()
    assert pcd is not None
    
    print(f"Min and max X: {np.min(pcd.points[:, 0]):.3f} to {np.max(pcd.points[:, 0]):.3f} mm")
    print(f"Min and max Y: {np.min(pcd.points[:, 1]):.3f} to {np.max(pcd.points[:, 1]):.3f} mm")
    print(f"Min and max Z: {np.min(pcd.points[:, 2]):.3f} to {np.max(pcd.points[:, 2]):.3f} mm")
    
    # ROI box
    # pcd = pcd.crop(o3d.geometry.AxisAlignedBoundingBox(min_bound=(7860, 7500, 400), max_bound=(8430, 8700, 2000)))
    
    # Set up visualization
    vis = o3d.visualization.Visualizer()  # type: ignore
    vis.create_window(window_name=f"PLY Viewer")
    vis.add_geometry(pcd)

    # Set rendering options for better visualization
    render_option = vis.get_render_option()
    render_option.point_size = 2.0  # Make points more visible
    render_option.background_color = np.array([0.1, 0.1, 0.1])  # Dark background

    # Run the visualizer
    vis.run()
    vis.destroy_window()
